#include <atomic>
#include <functional>
#include <thread>
#include <iostream>

template <typename T>
class ProducerConsumer {
private:
    static constexpr auto AlignSize = std::hardware_destructive_interference_size;
    
    static const size_t CAPACITY = 10;
    T buffer[CAPACITY];
    alignas(AlignSize) std::atomic<size_t> prod_head{0};
    alignas(AlignSize) std::atomic<size_t> cons_head{0};
    alignas(AlignSize) std::atomic<size_t> count{0};
    alignas(AlignSize) std::atomic<bool> isRunning{true};

public:
    void Stop() {
        isRunning.store(false, std::memory_order_release);
    }
    bool IsRunning() const {
        return isRunning.load(std::memory_order_acquire);
    }

    bool Produce(const T& item) {
        // Produce item
        size_t currentTail = cons_head.load();
        size_t nextTail = (currentTail + 1) % CAPACITY;
        if (nextTail == prod_head.load()) {
            // Queue is full
            return false;
        }
        if(!cons_head.compare_exchange_strong(currentTail, nextTail))
        {
            currentTail = cons_head.load();
            nextTail = (currentTail + 1) % CAPACITY;
        }
        buffer[currentTail] = std::move(item);
        return true;
    }
    bool Consume(T& item) {
        // Consume item
        size_t currentHead = prod_head.load();
        if (currentHead == cons_head.load()) {
            // Queue is empty
            return false;
        }
        size_t nextHead = (currentHead + 1) % CAPACITY;
        if (!prod_head.compare_exchange_strong(currentHead, nextHead)){
            currentHead = prod_head.load();
            nextHead = (currentHead + 1) % CAPACITY;
        }
        item = buffer[currentHead];
        return true;
    }

};



int main() {
    const size_t buffer_size = 16;
    const int N = 10;
    ProducerConsumer<int> pc;
    std::vector<int> consumed;

    std::thread prod_thread([&](){
        std::cout << "Producer started\n";
        for (int i = 0; i < N; ++i) {
            while (!pc.Produce(i)) {
                std::this_thread::yield();
            }
            std::cout << "Produced " << i << "\n";
        }
        pc.Stop();
        std::cout << "Producer stopped\n";
    });
    std::thread cons_thread([&](){
        std::cout << "Consumer started\n";
        while (true) {
            int item;
            if (pc.Consume(item)) {
                consumed.push_back(item);
                std::cout << "Consumed " << item << "\n";
            } else if (!pc.IsRunning()) {
                break;
            } else {
                std::this_thread::yield();
            }
        }
        std::cout << "Consumer stopped\n";
    });

    prod_thread.join();
    cons_thread.join();

    // Check if all items were consumed in order
    if (consumed.size() == static_cast<size_t>(N)) {
        bool correct = true;
        for (int i = 0; i < N; ++i) {
            if (consumed[i] != i) {
                correct = false;
                break;
            }
        }
        if (correct) {
            std::cout << "Test passed: all items consumed in order." << std::endl;
        } else {
            std::cout << "Test failed: items not in order." << std::endl;
        }
    } else {
        std::cout << "Test failed: incorrect number of items consumed. Expected " << N << ", got " << consumed.size() << std::endl;
    }

    return 0;
}
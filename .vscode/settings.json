{"files.associations": {"prod1,coo": "cpp", "new": "cpp", "stdexcept": "cpp", "array": "cpp", "__bit_reference": "cpp", "__hash_table": "cpp", "__locale": "cpp", "__node_handle": "cpp", "__split_buffer": "cpp", "__verbose_abort": "cpp", "bitset": "cpp", "cctype": "cpp", "clocale": "cpp", "cmath": "cpp", "cstdarg": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "deque": "cpp", "execution": "cpp", "initializer_list": "cpp", "ios": "cpp", "iosfwd": "cpp", "iostream": "cpp", "istream": "cpp", "limits": "cpp", "locale": "cpp", "mutex": "cpp", "optional": "cpp", "print": "cpp", "queue": "cpp", "ratio": "cpp", "stack": "cpp", "streambuf": "cpp", "string": "cpp", "string_view": "cpp", "typeinfo": "cpp", "unordered_map": "cpp", "variant": "cpp", "vector": "cpp", "condition_variable": "cpp", "memory": "cpp", "algorithm": "cpp"}}
#include <iostream>
#include <atomic>
#include <thread>


struct Data
{
  Data() = default;
  Data(const std::string&& ticker, double price, int volume) : _ticker(ticker), _price(price), _volume(volume) {};
  Data(Data&& other) : _ticker(std::move(other._ticker)), _price(other._price), _volume(other._volume) {};
  Data& operator=(Data&& other) {
    _ticker = std::move(other._ticker);
    _price = other._price;
    _volume = other._volume;
    return *this;
  };
  Data(const Data&) = delete;
  Data& operator=(const Data&) = delete;
  void Print() const {
    std::cout << "Ticker: " << _ticker << ", Price: " << _price << ", Volume: " << _volume << std::endl;
  }
  std::string _ticker;
  double _price{0};
  int _volume{0};
};


template <typename T>
class ProducerConsumer
{
private:
  std::vector<T> _data;
  std::atomic<size_t> _head {0};
  std::atomic<size_t> _head_final {0};
  std::atomic<size_t> _tail {0};
  std::atomic<size_t> _tail_final {0};
  std::atomic<bool> isRunning {true};
public:
  
  ProducerConsumer(const ProducerConsumer &) = delete;
  ProducerConsumer &operator=(const ProducerConsumer &) = delete;
  explicit ProducerConsumer(size_t size) : _data(size)
  {
    _head.store(0);
    _tail.store(0);
    _head_final.store(0);
    _tail_final.store(0);
  }
  ~ProducerConsumer() = default;

  size_t size() const {
      auto head = _head.load();
      auto tail = _tail.load();
      return (tail - head + _data.size()) % _data.size();
  }
  void Stop() {
    isRunning.store(false, std::memory_order_release);
    }
    bool IsRunning() const {
        return isRunning.load(std::memory_order_acquire);
    }

  bool Produce(T&& item) {
    size_t head;
    size_t tail;
    size_t next;

    if (!IsRunning()) {
        return false;
    }
    do
    {
        head = _head_final.load();
        tail = _tail.load();
        next = (tail + 1) % _data.size();

        if (next == head) 
        {
            return false; // queue is full
        }
    } while (!_tail.compare_exchange_weak(tail, next));
    
    _data[tail] = std::move(item);

    size_t tail_final;
    do
    {
        tail_final = _tail_final.load();
    } 
    while ((tail_final != tail) ||
        !_tail_final.compare_exchange_weak(tail_final, next));
    
    return true;
  }
 

  bool Consume(T& item) {
    size_t head; 
    size_t tail; 
    size_t next;
    do 
    { 
      head = _head.load();
      tail = _tail_final.load();

      if (head == tail) { 
        return false; // queue is empty
      }
      next = (head + 1) % _data.size();

    } while (!_head.compare_exchange_weak(head, next));
    item = std::move(_data[head]);

    size_t head_final;
    do
    {
        head_final = _head_final.load();
    }
    while ((head_final != head) ||
        !_head_final.compare_exchange_weak(head_final, next));
    
    return true;
  }


  bool produce(std::vector<T>&& items) {
    auto head = _head.load();
    auto tail = _tail.load();

    if ((tail - head + _data.size()) % _data.size() + items.size() > _data.size()) {

      return false; // queue is full
    }
    for (auto& item : items) {
      _data[tail] = std::move(item);
      tail = (tail + 1) % _data.size();
    }
    _tail.store(tail);

    return true;
  }
  bool consume(std::vector<T>& items) {
    size_t head;
    size_t tail;
    size_t next;
    do 
    {
        head = _head.load();
        tail = _tail.load();
        if (head == tail) 
        {
            return false; // queue is empty
        }
        next  = (head + 1) % _data.size();
    }
    while (!_head.compare_exchange_weak(head, next));
    //_head.store(head);

    return true;
  }

};


int main() {
  std::cout << "Hello, World!" << std::endl;
  const int N = 10;
  std::vector<int> consumed;
  ProducerConsumer<int> pc(10);

  std::thread prod_thread([&](){
    std::cout << "Producer started\n";
    for (int i = 0; i < N; ++i) {
        while (!pc.Produce(std::move(i))) {
            std::this_thread::yield();
        }
        std::cout << "Produced " << i << "\n";
    }
    pc.Stop();
    std::cout << "Producer stopped\n";
});
std::thread cons_thread([&](){
    std::cout << "Consumer started\n";
    while (true) {
        int item;
        if (pc.Consume(item)) {
            consumed.push_back(item);
            std::cout << "Consumed " << item << "\n";
        } else if (!pc.IsRunning()) {
            break;
        } else {
            std::this_thread::yield();
        }
    }
    std::cout << "Consumer stopped\n";
});

prod_thread.join();
cons_thread.join();

  return 0;
}
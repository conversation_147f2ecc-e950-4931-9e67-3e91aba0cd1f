#include <iostream>
#include <atomic>
#include <array>
#include <exception>
#include <thread>
static const size_t BUFFER_SIZE = 10;
template<typename T, size_t N = BUFFER_SIZE>
class ProducerConsumer
{
private:
    struct Node {
        T data;
        std::atomic<bool> in_use;
        Node() = default;
    };
    static constexpr size_t DESTRUCTIVE_SIZE = std::hardware_destructive_interference_size;
    std::array<Node, N> buffer;
    std::atomic<size_t> prod_head{0};  
    std::atomic<size_t> prod_tail{0};   // commit produce 
    std::atomic<size_t> cons_head{0};
    std::atomic<size_t> cons_tail{0};   // commit consume
    
    
    std::atomic<bool> isRunning {true};

    static_assert(std::is_move_constructible<T>::value, "T must be move constructible");
    static_assert(std::is_move_assignable<T>::value, "T must be move assignable");

public:
    ProducerConsumer() {
        if (N == 0) {
            throw std::invalid_argument("Buffer size must be greater than 0");
        }
    }
    
    // Disable copy, allow move
    ProducerConsumer(const ProducerConsumer&) = delete;
    ProducerConsumer& operator=(const ProducerConsumer&) = delete;
    ProducerConsumer(ProducerConsumer&&) = default;
    ProducerConsumer& operator=(ProducerConsumer&&) = default;
    
    void terminate() {
        isRunning.store(false);
    }
    bool isTerminated() {
        return !isRunning.load();
    }

    bool produce(T&& item) {
        // Produce an item and add it to the buffer
        if (isTerminated()) return false; // If the producer is terminated, do not produce any more items

        size_t current_head;
        size_t next_head;

        do
        {
            current_head = prod_head.load();
            next_head = (current_head + 1) % N;
            if (next_head == cons_head.load()) {
                // Buffer is full, cannot produce
                std::cout <<"queue is full\n";
                return false;
            }
    
        } 
        while (!prod_head.compare_exchange_weak(current_head, next_head)); // no other producer is writing to the buffer

        buffer[current_head].data = std::move(item);
        buffer[current_head].in_use.store(true);


        while (!(prod_tail.compare_exchange_weak(current_head, next_head))) // CAS
        {
            std::this_thread::yield(); // Yield to other threads
        }    
        
        return true; // Successfully produced an item
    }

bool consume(T& item) {
    // Remove an item from the buffer and consume it
    if (isTerminated()) return false; // If the consumer is terminated, do not consume any more items
    
    size_t current_head;
    size_t next_head;
    
    do
    {
        current_head = cons_head.load();
        if (current_head == prod_head.load() || current_head == prod_tail.load()) {        
            return false;
        }   
        next_head = (current_head + 1) % N;

    } while (!cons_head.compare_exchange_weak(current_head, next_head)); // no other consumer is reading from the buffer
    
    // Remove an item from the buffer and consume it
    item = std::move(buffer[current_head].data);    
    
    while (!(cons_tail.compare_exchange_weak(current_head, next_head))) // CAS
    {
        std::this_thread::yield(); // Yield to other threads
    }   
    
    return true; // Successfully consumed an item
}
    
};

int main() {
    std::cout << "Hello, World!\n";
    std::cout << "std::hardware_destructive_interference_size" << std::hardware_destructive_interference_size << " \n";
    try
    {
        ProducerConsumer <int> pc;

        std::thread producer1([&](){
            for (int i = 0; i < 10; ++i) {
                pc.produce(std::move(i));
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            }
        }); // Producer thread
        std::thread producer2([&](){
            for (int i = 10; i < 20; ++i) {
                pc.produce(std::move(i));
                std::cout << "produced " << i << std::endl;
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(1000));
            pc.terminate(); // Signal the consumer to stop
        }); // Producer thread
        std::thread consumer([&](){
            while (true) {
                int item;
                if (pc.consume(item))
                {
                    std::cout << "Consumed: " << item << '\n';
                    std::this_thread::sleep_for(std::chrono::milliseconds(150));
                }
                else if (pc.isTerminated())
                {
                    std::cout << "Terminated\n";
                    break;
                }
                else {
                    std::this_thread::yield();
                }
            }
        });
        producer1.join();
        producer2.join();
        consumer.join();
    }
    catch(const std::exception& e)
    {
        std::cerr << e.what() << '\n';
    }
    
    
    return 0;

}
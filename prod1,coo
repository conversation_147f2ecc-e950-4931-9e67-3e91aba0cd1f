#include <queue>
#include <mutex>
#include <condition_variable>
#include <atomic>
#include <functional>
#include <thread>

template<typename T>
class ProducerConsumer {
private:
    std::queue<T> buffer; // Shared buffer
    std::mutex mtx; // Synchronization mutex
    std::condition_variable not_empty; // Notify when buffer not empty
    std::atomic<bool> done{false}; // Termination flag
    std::function<T()> producerFunc; // Generate items
    std::function<void(T)> consumerFunc; // Process items

public:
    ProducerConsumer(std::function<T()> prod, std::function<void(T)> cons)
        : producerFunc(prod), consumerFunc(cons) {}

    void produceLoop() {
        while (!done) {
            T item = producerFunc();
            {
                std::lock_guard<std::mutex> lock(mtx);
                buffer.push(std::move(item));
            }
            not_empty.notify_one();
        }
    }

    void consumeLoop() {
        while (true) {
            T item;
            {
                std::unique_lock<std::mutex> lock(mtx);
                while (buffer.empty() && !done) {
                    not_empty.wait(lock);
                }
                if (buffer.empty() && done) {
                    return;
                }
                item = std::move(buffer.front());
                buffer.pop();
            }
            consumerFunc(std::move(item));
        }
    }

    void stop() {
        {
            std::lock_guard<std::mutex> lock(mtx);
            done = true;
        }
        not_empty.notify_all();
    }
};